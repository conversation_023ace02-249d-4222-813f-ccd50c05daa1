<template>
  <div class="goods-detail">
    <!-- 骨架屏 -->
    <GoodsDetailSkeleton v-if="isLoading" />

    <!-- 商品内容 -->
    <div v-else class="goods-content">
      <!-- 商品图片 -->
      <section class="image-section">
        <GoodsImageSwiper :media-list="goodsMediaList" :loop="true" :autoplay="true" @image-click="handleImagePreview"
          @video-click="handleVideoPlay" @slide-change="handleSlideChange" />
      </section>

      <!-- 商品基础信息 -->
      <section class="basic-info-section">
        <!-- 价格 -->
        <div class="price-wrapper">
          <PriceDisplay :price="goodsInfo.price" size="large" color="orange" />
          <span v-if="goodsInfo.originalPrice" class="price-original">
            日常价 ¥{{ (goodsInfo.originalPrice / 100).toFixed(2) }}
          </span>
        </div>

        <!-- 标题 -->
        <h1 class="title">{{ goodsInfo.name }}</h1>
      </section>

      <!-- 营销活动 -->
      <section class="marketing-section"
        v-if="marketTemplatesType1 && marketTemplatesType1.length > 0 && marketTemplatesType1[0].upImage">
        <div class="goods-marketing">
          <img @click="marketingBtn()" :src="marketTemplatesType1[0].upImage" alt="" />
        </div>
      </section>

      <!-- 规格选择 -->
      <section class="spec-section">
        <div class="spec-wrapper" @click="showSpecPopup = true">
          <img src="./assets/round_check.png" alt="" class="spec-icon" />
          <div class="spec-main">
            <div class="spec-selected">
              <span class="spec-label">已选：</span>
              <span class="spec-value">{{ selectedSpec }}</span>
              <img src="@/static/images/arrow-right-gray.png" alt="" class="arrow-icon" />
            </div>
            <!-- 规格选项 -->
            <div class="spec-options-wrapper">
              <div class="spec-options" ref="specOptionsRef">
                <div v-for="spec in enhancedSpecOptions" :key="spec.id" class="spec-option"
                  :class="{ 'is-active': spec.selected, 'is-disabled': spec.disabled, 'active': spec.selected }"
                  @click.stop="selectSpec(spec)">
                  <img v-if="spec.image" :src="spec.image" alt="" class="spec-image" />
                  <img v-if="spec.selected" src="./assets/diagonal-hook.png" alt="" class="spec-check" />
                </div>
              </div>
              <div class="spec-count">共{{ enhancedSpecOptions.length }}款可选</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 优惠活动 -->
      <section class="promotion-section"
        v-if="marketTemplatesType4 && marketTemplatesType4.length > 0 && getBizCode() === 'ziying'">
        <div class="promotion-activity">
          <div class="promotion-activity-title">
            优惠活动
          </div>
          <div class="promotion-activity-content">
            <div class="promotion-item" v-for="(item, index) in marketTemplatesType4" :key="index"
              @click="goToPromotionDetail(item)">
              <img :src="item.activityImage" alt="优惠活动" class="promotion-heart-img">
              <div class="promotion-content">
                {{ item.activityName }}
              </div>
              <div class="promotion-arrow">
                <span class="promotion-desc">{{ item.activityDec }}</span>
                <i class="arrow-icon"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 配送信息 -->
      <section class="delivery-section">
        <div class="delivery-wrapper">
          <div class="delivery-main" @click="handleAddressClick">
            <img src="./assets/logistics.png" alt="" class="delivery-icon" />
            <div class="delivery-content">
              <div class="delivery-location">
                <span class="delivery-text">{{ deliveryInfo.location }}</span>
              </div>
              <div v-if="isShowLogisticsServices" class="delivery-time">
                <!-- 京东商品且有预测内容时显示 -->
                <template v-if="isJD && logisticsServicesInfo.predictContent">
                  <img v-if="logisticsServicesInfo.logisticsType === 1" src="./assets/jdE.png" alt=""
                    class="delivery-badge" />
                  <img
                    v-else-if="logisticsServicesInfo.logisticsType === 0 || logisticsServicesInfo.logisticsType === 2"
                    src="./assets/threeE.png" alt="" class="delivery-badge" />
                  <span class="delivery-text" v-html="logisticsServicesInfo.predictContent"></span>
                </template>
                <!-- 非京东商品或无预测内容时的默认显示 -->
                <template v-else>
                  <img src="./assets/threeE.png" alt="" class="delivery-badge" />
                  <span class="delivery-text">预计48小时之内发货</span>
                </template>
                <img src="@/static/images/arrow-right-gray.png" alt="" class="arrow-icon" />
              </div>
            </div>
          </div>
          <div class="delivery-return">
            <img src="./assets/7days.png" alt="" class="return-icon" />
            <span class="delivery-text">{{ deliveryInfo.returnPolicy }}</span>
          </div>
          <div class="delivery-service">
            <img src="./assets/guarantee.png" alt="" class="service-icon" />
            <span class="delivery-text">店铺售后由<span class="text-highlight">沃百富商城</span>提供服务</span>
          </div>
        </div>
      </section>

      <!-- 商品介绍 -->
      <section class="introduce-section" ref="introduceSectionRef">
        <!-- 懒加载占位符 -->
        <div v-if="!isIntroduceVisible" class="introduce-placeholder">
          <div class="introduce-placeholder__title">商品信息</div>
          <div class="introduce-placeholder__content">
            <div class="introduce-placeholder__skeleton"></div>
          </div>
        </div>
        <!-- 实际组件 -->
        <GoodsIntroduce v-if="isIntroduceVisible" :currentSKU="currentSKUForIntroduce" />
      </section>
    </div>

    <!-- 底部操作栏占位符 -->
    <WoActionBarPlaceholder />

    <!-- 状态提示信息 -->
    <div v-if="isDataGet" class="status-tips-overlay">
      <div class="status-tips-container">
        <div class="status-tip tips-state" v-if="!onSaleState">
          <div class="tip-icon">⚠️</div>
          <div class="tip-content">
            <div class="tip-title">商品已下架</div>
            <div class="tip-message">该商品已下架，请选购其他商品!</div>
          </div>
        </div>
        <div class="status-tip tips-stock" v-if="onSaleState && !stockState">
          <div class="tip-icon">📦</div>
          <div class="tip-content">
            <div class="tip-title">暂时无货</div>
            <div class="tip-message">所选地区暂时无货，非常抱歉！</div>
          </div>
        </div>
        <div class="status-tip tips-permission" v-if="!userStatus">
          <div class="tip-icon">🚫</div>
          <div class="tip-content">
            <div class="tip-title">无购买资格</div>
            <div class="tip-message">您暂无购买资格，非常抱歉！</div>
          </div>
        </div>
        <div class="status-tip tips-region" v-if="!regionalSalesState">
          <div class="tip-icon">📍</div>
          <div class="tip-content">
            <div class="tip-title">区域限制</div>
            <div class="tip-message">抱歉，此商品在所选区域暂不支持销售!</div>
          </div>
        </div>
        <div class="status-tip tips-limit" v-if="!limitState">
          <div class="tip-icon">🔢</div>
          <div class="tip-content">
            <div class="tip-title">限购商品</div>
            <div class="tip-message">该商品限购，请选购其他商品!</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <WoActionBar>
      <div class="action-bar">
        <!-- 操作区域 -->
        <div class="action-content">
          <div class="cart-wrapper">
            <div class="cart-icon-wrapper" @click="goToCart">
              <van-badge :content="cartCount" :show-zero="false" :max="99" class="cart-badge">
                <img src="./assets/cart-icon.png" alt="购物车" class="cart-icon" />
              </van-badge>
            </div>
            <span class="cart-text">购物车</span>
          </div>

          <div class="buttons-wrapper">
            <WoButton type="secondary" size="medium" :disabled="cartButtonDisabledStatus" @click="addToCart">
              加入购物车
            </WoButton>
            <WoButton type="gradient" size="medium" :disabled="cartButtonDisabledStatus" @click="buyNow">
              立即购买
            </WoButton>
          </div>
        </div>
      </div>
    </WoActionBar>

    <!-- 规格选择弹窗 -->
    <SpecSelectionPopup v-model:visible="showSpecPopup" :address-info="addressInfo" :goods-info="skuGoodsInfo"
      :spec-options="specOptions" :initial-quantity="goodsNum" :action-type="specActionType"
      :cart-button-disabled="cartButtonDisabledStatus" @select-spec="selectSpec" @add-to-cart="handleAddToCart"
      @buy-now="handleBuyNow" @quantity-change="handleQuantityChange" />

    <!-- 地址选择弹窗 -->
    <AddressQuickSelectionPopup v-model:visible="showAddressPopup" :show-tips="true"
      :tips-text="logisticsServicesInfo.predictContent" @select="handleAddressSelect"
      @close="handleAddressPopupClose" />

    <!-- 悬浮按钮 -->
    <FloatingBubble :offset="200" :isShowCart="false"/>

    <!-- 右上角分享功能 -->
    <ShareDropdown :visible="showShareMenu" @share="onDropdownShare" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useGoodsDetail } from '@/hooks/hooks.js'
import { useUserStore } from '@/store/modules/user.js'
import { useNewCartStore } from '@/store/modules/newCart.js'
import {
  productIntroduction,
  queryPredictSkuPromise,
  checkSkuSale,
  setFrontCache,
  isWhiteUserLimitCheck,
  getLimitAreaList
} from '@/api/interface/goods.js'
import PriceDisplay from '@/components/Common/PriceDisplay.vue'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import GoodsImageSwiper from '@views/WoMall/Goods/components/GoodsImageSwiper.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import GoodsIntroduce from '@views/WoMall/Goods/components/GoodsIntroduce.vue'
import AddressQuickSelectionPopup from '@/components/Address/AddressQuickSelectionPopup.vue'
import SpecSelectionPopup from '@views/WoMall/Goods/components/SpecSelectionPopup.vue'
import GoodsDetailSkeleton from '@views/WoMall/Goods/components/GoodsDetailSkeleton.vue'
import { getBizCode } from '@utils/curEnv.js'
import { debounce } from 'lodash-es'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { useAlert } from '@/hooks/index.js'
import { getBuyNowGoods, jdAddressCheck } from '@/api/index.js'
import { buyProductNow, buyProductNowSession } from '@/utils/storage.js'
import { getActiveList } from '@/api/interface/goods.js'
import { urlAppend } from 'commonkit'
import FloatingBubble from '@components/FilterTools/FloatingBubble.vue'
import ShareDropdown from '@views/WoMall/Goods/components/ShareDropdown.vue'
import { getDefaultShareUrl, shareData } from '@/utils/share.js'
import { log, setWeiXinShareData, share } from 'commonkit'

const $alert = useAlert()

const route = useRoute()
const router = useRouter()
const goodsId = route.params.goodsId
const skuId = route.params.skuId
const userStore = useUserStore()
const newCartStore = useNewCartStore()
const isShowLogisticsServices = ref(true)
const introduceSectionRef = ref(null)
const isIntroduceVisible = ref(false)
const hasIntroduceDataLoaded = ref(false)
const hasUserScrolled = ref(false) // 新增：标记用户是否已经滚动过
const initialLoadComplete = ref(false) // 新增：标记初始加载是否完成

// 懒加载的商品介绍数据
const currentSKUForIntroduce = computed(() => {
  if (!isIntroduceVisible.value || !hasIntroduceDataLoaded.value) {
    return {}
  }
  return {
    ...currentSku.value,
    introduction: productIntroductionData.value
  }
})

// Intersection Observer 实例
let introduceObserver = null
// 使用商品详情 hook
const {
  spu,
  curSpecs,
  curSkuId,
  querySpu,
  querySku,
  querySpecsList,
  setSpecs,
  queryCurSpecs,
  queryDisabledSpecs,
  isSpecsComplete
} = useGoodsDetail(goodsId, skuId)

// 响应式数据

// 从store获取购物车数量
const cartCount = computed(() => {
  return newCartStore.countByGoods
})
const isLoading = ref(true)
const contentLoaded = ref(false)
const showSpecPopup = ref(false)
const showAddressPopup = ref(false)
const specActionType = ref(1)
const showShareMenu = ref(true)
const previewImageIndex = ref(0)
const detailErr = ref(false)
const isJD = ref(false)
const goodsNum = ref(1)
const productIntroductionData = ref('')
const logisticsServicesInfo = ref({
  logisticsType: 1,
  returnRuleStr: '',
  predictContent: '预计48小时之内发货',
  isJD: false
})

// 商品状态相关数据
const isDataGet = ref(false)
const limitState = ref(true) // 白名单用户是否限购，true没有限购，false有限购
const regionalSalesState = ref(true) // 所选区域暂不支持销售，true没有区域限制，false有区域限制

// 营销活动相关数据
const marketTemplatesType1 = ref([]) // 营销位类型1
const marketTemplatesType4 = ref([]) // 营销位类型4
const reducePrice = ref(0) // 减免价格

// 规格选择区域的引用
const specOptionsRef = ref(null)

const specsData = computed(() => {
  return {
    specsList: querySpecsList(),
    curSpecs: queryCurSpecs ? queryCurSpecs() : [],
    disabledSpecs: queryDisabledSpecs ? queryDisabledSpecs() : [],
    currentSku: currentSku.value
  }
})

// 限购逻辑
const xgObj = computed(() => {
  const limitTemplate = spu.value?.limitTemplate
  let limitText = ''
  if (limitTemplate && limitTemplate?.limitCountType) {
    switch (limitTemplate.limitCountType) {
      case '1':
        limitText = `每人每次限购${limitTemplate.limitNum}件`
        break
      case '2':
        limitText = `每人限购${limitTemplate.limitNum}件`
        break
      default:
        limitText = ''
    }
  }
  const limitNum = limitTemplate && limitTemplate?.limitNum ? limitTemplate.limitNum : 1
  return {
    isXg: spu.value?.isXg === '1',
    limitNum,
    limitText
  }
})

// 起购逻辑
const lowestBuyObj = computed(() => {
  const lowestBuyValue = currentSku.value?.lowestBuy
  const isLowestBuy = lowestBuyValue ? parseInt(lowestBuyValue, 10) > 1 : false
  const lowestBuyNum = lowestBuyValue ? parseInt(lowestBuyValue, 10) : 1
  const lowestBuyText = lowestBuyValue ? `${lowestBuyNum}件起购` : ''

  return {
    isLowestBuy,
    lowestBuyNum,
    lowestBuyText
  }
})


const addressInfo = computed(() => {
  const curAddrInfo = userStore.curAddressInfo
  console.warn(2131331, curAddrInfo);

  return {
    provinceName: curAddrInfo.provinceName || '',
    cityName: curAddrInfo.cityName || '',
    countyName: curAddrInfo.countyName || '',
    townName: curAddrInfo.townName || '',
    addrDetail: curAddrInfo.addrDetail || '',
    receiverName: curAddrInfo.recName || '',
    receiverPhone: curAddrInfo.recPhone || ''
  }
})

const skuGoodsInfo = computed(() => {
  const sku = currentSku.value
  const xg = xgObj.value
  const lowestBuy = lowestBuyObj.value

  // 确定购买限制
  let purchaseLimit = 999 // 默认限制
  let purchaseLimitType = 'none'
  let purchaseLimitText = ''

  // 优先处理起购逻辑
  if (lowestBuy.isLowestBuy) {
    purchaseLimit = lowestBuy.lowestBuyNum
    purchaseLimitType = 'minimum'
    purchaseLimitText = lowestBuy.lowestBuyText
  }

  // 处理限购逻辑
  if (xg.isXg && xg.limitText) {
    purchaseLimit = xg.limitNum
    purchaseLimitType = xg.limitText.includes('每次') ? 'perTime' : 'perPerson'
    purchaseLimitText = xg.limitText
  }

  return {
    image: sku.listImageUrl || '',
    price: sku.price || 0,
    supplierSkuId: sku.supplierSkuId || '',
    stock: sku.stock || 0,
    purchaseLimit,
    purchaseLimitType,
    purchaseLimitText,
    currSku: sku,
    xgObj: xg,
    lowestBuyObj: lowestBuy
  }
})

// 防抖的异步数据更新函数
const debouncedUpdateGoodsInfo = debounce(async () => {
  const sku = querySku()
  if (sku && sku.supplierCode && sku.supplierCode.indexOf('jd_') > -1) {
    try {
      await Promise.all([
        queryPredictSku(),
        getProductIntroduction()
      ])
    } catch (error) {
      console.error('更新商品信息失败:', error)
    }
  }
}, 300)

// 监听用户滚动行为
const handleUserScroll = () => {
  if (!hasUserScrolled.value && initialLoadComplete.value) {
    hasUserScrolled.value = true
    console.log('检测到用户滚动行为')
  }
}

// 初始化懒加载观察器
const initIntroduceObserver = () => {
  if (!introduceSectionRef.value) return

  introduceObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !isIntroduceVisible.value) {
          // 只有在用户滚动过或者区域不在初始可视区域时才加载
          if (hasUserScrolled.value || !isInInitialViewport(entry.target)) {
            console.log('商品介绍区域进入可视区域，开始加载')
            isIntroduceVisible.value = true

            // 开始加载数据
            loadIntroduceData()

            // 停止观察，避免重复触发
            introduceObserver.unobserve(entry.target)
          }
        }
      })
    },
    {
      // 提前 50px 开始加载，但不要太早
      rootMargin: '50px 0px',
      threshold: 0.1
    }
  )

  introduceObserver.observe(introduceSectionRef.value)
}

// 检查元素是否在初始视口内
const isInInitialViewport = (element) => {
  const rect = element.getBoundingClientRect()
  const viewportHeight = window.innerHeight || document.documentElement.clientHeight

  // 如果元素顶部在视口内，认为是初始可见的
  return rect.top < viewportHeight && rect.top >= 0
}

// 加载商品介绍数据
const loadIntroduceData = async () => {
  const sku = querySku()
  if (!sku) {
    hasIntroduceDataLoaded.value = true
    return
  }

  try {
    // 如果是京东商品，需要请求接口获取数据
    if (sku.supplierCode && sku.supplierCode.indexOf('jd_') > -1) {
      console.log('开始请求京东商品介绍数据')
      await getProductIntroduction()
    }

    hasIntroduceDataLoaded.value = true
    console.log('商品介绍数据加载完成')
  } catch (error) {
    console.error('加载商品介绍数据失败:', error)
    hasIntroduceDataLoaded.value = true
  }
}

// 滚动位置管理
let savedScrollPosition = 0
const saveScrollPosition = () => {
  savedScrollPosition = window.scrollY || document.documentElement.scrollTop
}

const restoreScrollPosition = () => {
  requestAnimationFrame(() => {
    window.scrollTo(0, savedScrollPosition)
  })
}

// 计算属性
const currentSku = computed(() => {
  return querySku() || {}
})

const goodsInfo = computed(() => {
  const sku = currentSku.value
  if (!sku || !spu.value) {
    return {
      name: '',
      price: 0,
      originalPrice: 0,
      imageUrl: ''
    }
  }
  return {
    name: sku.name || '',
    price: sku.price || 0,
    originalPrice: sku.originalPrice || 0,
    imageUrl: sku.listImageUrl || ''
  }
})

const currentSKU = computed(() => {
  const sku = currentSku.value
  return {
    ...sku
  }
})

const goodsMediaList = computed(() => {
  const sku = currentSku.value
  if (!sku || !sku.detailImageUrl) {
    return []
  }
  return sku.detailImageUrl.map(url => ({
    type: 'image',
    url,
    alt: '商品图片'
  }))
})

const specOptions = computed(() => {
  // 获取规格数据，参考 GoodsChoose.vue 的逻辑
  const specsList = querySpecsList()
  const curSpecs = queryCurSpecs ? queryCurSpecs() : []
  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  // 如果没有规格数据，返回默认规格结构
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return {
      specsList: [],
      curSpecs: ['默认规格'], // 默认选中默认规格
      curDisabledSpecs: [],
    }
  }

  return {
    specsList: specsList,
    curSpecs: curSpecs,
    curDisabledSpecs: disabledSpecs,
  }
})

// 缓存的规格选项数据，在商品初始化时计算一次
const cachedSpecOptions = ref([])

// 初始化规格选项数据
const initSpecOptions = () => {
  if (!spu.value || !spu.value.skuList) {
    cachedSpecOptions.value = []
    return
  }

  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  const options = spu.value.skuList.map(sku => {
    const isDisabled = checkSkuDisabled(sku, disabledSpecs)

    return {
      id: sku.skuId,
      name: getSkuDisplayName(sku),
      image: sku.listImageUrl,
      disabled: isDisabled,
      skuData: sku,
    }
  })

  // 只缓存可选择的SKU（过滤掉禁用的）
  cachedSpecOptions.value = options.filter(option => !option.disabled)

  console.log('初始化规格选项数据:', {
    totalOptions: options.length,
    selectableOptions: cachedSpecOptions.value.length
  })
}

// 动态计算选中状态的规格选项
const enhancedSpecOptions = computed(() => {
  return cachedSpecOptions.value.map(option => ({
    ...option,
    selected: option.id === curSkuId.value
  }))
})


// 检查SKU是否被禁用
const checkSkuDisabled = (sku, disabledSpecs) => {
  const skuSpecs = getSkuSpecs(sku)
  return skuSpecs.some(spec => disabledSpecs.includes(spec))
}

// 获取SKU的规格数组
const getSkuSpecs = (sku) => {
  const { param, param1, param2, param3 } = sku
  return ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
    .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
      p !== '_p2_undefined' && p !== '_p3_undefined')
}

const selectedSpec = computed(() => {
  const sku = currentSku.value
  const specsList = querySpecsList()

  // 如果没有规格数据，显示默认规格
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return `默认规格 ${goodsNum.value}件`
  }

  if (!sku || !isSpecsComplete()) {
    return `默认 ${goodsNum.value}件`
  }

  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)

  const specText = specs.join('，') || '默认'
  return `${specText} ${goodsNum.value}件`
})

const deliveryInfo = computed(() => {
  const curAddrInfo = userStore.curAddressInfo
  const location = `${curAddrInfo.provinceName} ${curAddrInfo.cityName} ${curAddrInfo.countyName} ${curAddrInfo.townName || ''}`.trim()

  return {
    location: location || '配送地址',
    predictContent: logisticsServicesInfo.value.predictContent || (isJD.value ? 'JD配送' : '普通配送'),
    returnPolicy: logisticsServicesInfo.value.returnRuleStr || '7天无理由退货',
    service: '店铺售后由沃百富商城提供服务'
  }
})

// 商品详情加载
const loadGoodsDetail = async () => {
  isShowLogisticsServices.value = getBizCode() !== 'fupin'
  try {
    const json = await querySpu()
    console.warn('商品详情接口返回数据:', json)

    if (json.code !== '0000') {
      if (json.code === '8888') {
        console.warn('此商品信息更新中，暂时无法购买，请您选购其他商品。')
      } else {
        console.warn(json.msg)
      }
      detailErr.value = true
      isLoading.value = false
      return
    }

    if (!spu.value) {
      console.warn('商品数据为空')
      isLoading.value = false
      return
    }

    // 检查当前sku
    const sku = querySku()
    if (sku && sku.supplierCode) {
      isJD.value = sku.supplierCode.indexOf('jd_') > -1
    }

    // 初始化规格选项数据
    initSpecOptions()

    // 处理营销活动数据
    processMarketingTemplates()

    // 基础数据加载完成，可以显示页面内容
    isLoading.value = false
    isDataGet.value = true

    // 等待DOM更新后再执行后续操作
    await nextTick()

    // 添加内容加载动画
    setTimeout(() => {
      contentLoaded.value = true
      // 标记初始加载完成
      initialLoadComplete.value = true
    }, 100)

    if (isJD.value) {
      // 异步加载额外信息，不阻塞页面渲染
      // 移除 getProductIntroduction()，改为懒加载
      Promise.all([
        queryPredictSku(),
        checkIsSkuSale()
      ]).catch(error => {
        console.error('加载额外商品信息失败:', error)
      })
    }

    // 数据加载完成后，延迟滚动到选中的规格
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 200)

    // 根据起购要求设置初始数量
    const lowestBuy = lowestBuyObj.value
    if (lowestBuy.isLowestBuy) {
      goodsNum.value = lowestBuy.lowestBuyNum
    }

    // 延迟初始化商品介绍懒加载观察器，确保页面稳定后再开始监听
    setTimeout(() => {
      initIntroduceObserver()
      // 开始监听用户滚动
      window.addEventListener('scroll', handleUserScroll, { passive: true })
    }, 500) // 增加延迟时间

    // 检查白名单用户限制
    await checkWhiteUserLimit()

    // 查询商品限制销售区域
    await querySale()

   if(userStore.isLogin) {
    await addressCheck()
   }

    // 激活客户端分享
    await shareInit()

  } catch (error) {
    console.error('加载商品详情失败:', error)
    detailErr.value = true
    isLoading.value = false
  }
}

const getProductIntroduction = async () => {
  const sku = querySku()
  if (!sku) return

  const { supplierSkuId, supplierCode } = sku
  const [err, json] = await productIntroduction({
    supplierSkuId,
    supplierCode
  })
  if (!err) {
    // 直接更新响应式数据
    productIntroductionData.value = json
  }
}

// 查询物流配送时间
const queryPredictSku = async () => {
  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  const sku = currentSku.value
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      predictContent: '预计48小时之内发货'
    }
    return
  }

  const params = {
    supplierCode: sku.supplierCode,
    supplierSkuId: sku.supplierSkuId,
    skuNum: goodsNum.value,
    addressInfoStr: addressInfo
  }

  const [err, res] = await queryPredictSkuPromise(params)
  if (!err) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res
    }
    return
  }

  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    predictContent: '预计48小时之内发货'
  }
}

// 检查SKU是否可售
const checkIsSkuSale = async () => {
  const sku = currentSku.value
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) return

  const params = {
    supplierCode: sku.supplierCode,
    supplierSkuId: sku.supplierSkuId
  }

  const [err, res] = await checkSkuSale(params)
  if (!err) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res[0]
    }
    return
  }

  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    logisticsType: 0,
    returnRuleStr: ''
  }
}

// 获取SKU的显示名称
const getSkuDisplayName = (sku) => {
  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)
  return specs.join('，') || '默认'
}

// 根据SKU更新规格状态
const updateSpecsFromSku = (sku) => {
  const specs = []
  if (sku.param) specs.push('_p0_' + sku.param)
  if (sku.param1) specs.push('_p1_' + sku.param1)
  if (sku.param2) specs.push('_p2_' + sku.param2)
  if (sku.param3) specs.push('_p3_' + sku.param3)

  // 更新当前规格状态
  curSpecs.value = specs
}

// 检查白名单用户限制
const checkWhiteUserLimit = async () => {
  const goodsDetail = spu.value
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1') {
    // 如果登录的话，查询用户是否有资格在白名单内
    if (userStore.isLogin) {
      const [err, json] = await isWhiteUserLimitCheck(goodsId)
      if (!err) {
        limitState.value = json
      }
    }
  }
}

// 查询商品限制销售区域
const querySale = async () => {
  const info = userStore.curAddressInfo
  const params = {
    area: JSON.stringify({
      provinceId: info.provinceId,
      cityId: info.cityId,
      countyId: info.countyId,
      townId: info.townId
    }),
    goodsIdList: goodsId
  }

  // 商品数据正常时才进行区域限购查询
  if (!detailErr.value) {
    if (userStore.isLogin) {
      const [err, json] = await getLimitAreaList(params)
      if (!err && json) {
        regionalSalesState.value = json.length <= 0
      }
    }
    isDataGet.value = true
  } else {
    isDataGet.value = false
  }
}

// 地址检查方法
const addressCheck = async () => {
  showLoadingToast()
  const [err, json] = await jdAddressCheck()
  closeToast()

  if (err) {
    showToast(err.msg)
    return false
  }

  if (!json) {
    await $alert({
      title: '',
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
      confirmButtonText: '修改地址',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: () => {
        // 确认修改地址
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      }
    })
    return false
  }

  return true
}

// 营销活动相关方法
const processMarketingTemplates = () => {
  const goodsDetail = spu.value
  if (!goodsDetail || !goodsDetail.marketTemplates) {
    return
  }

  // 活动营销位信息 （templateType 为 营销位类型）
  marketTemplatesType1.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '1'
  )
  marketTemplatesType4.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '4'
  )

  // 如果有营销位类型1，获取电子券信息
  if (userStore.isLogin && marketTemplatesType1.value.length > 0) {
    getElectronic()
  }
}

const getElectronic = async () => {
  if (userStore.isLogin && marketTemplatesType1.value && marketTemplatesType1.value.length > 0) {
    if (
      marketTemplatesType1.value?.[0] &&
      marketTemplatesType1.value[0].reqType === '1' &&
      marketTemplatesType1.value[0].templateNo === 'wxy618'
    ) {
      const [err, json] = await getActiveList({ templateNo: marketTemplatesType1.value[0].templateNo || '' })
      if (!err) {
        reducePrice.value = json
      }
    }
  }
}

const marketingBtn = () => {
  if (!marketTemplatesType1.value || marketTemplatesType1.value.length === 0) {
    return
  }

  const { reqType, reqUrl, templateNo } = marketTemplatesType1.value[0]
  // reqType=1 跳转链接形式
  if (reqType === '1') {
    if (templateNo === 'wxy618') {
      // 保证金活动，特殊处理，拼接 goodsId,skuId,callback
      const host = window.location.origin
      const path = import.meta.env.VITE_BASE_URL
      const callbackUrl = host + path + `/goodsdetail/${goodsId}/${currentSku.value.skuId}?distri_biz_code=ziying`
      if (reducePrice.value > 0 && Number(goodsInfo.value.price) === 0) {
        showToast('您已经参与过该活动，请下次再试吧')
        return
      }
      window.location.href = urlAppend(reqUrl, {
        goodsId: goodsId,
        skuId: currentSku.value.skuId,
        callback: callbackUrl
      })
    } else {
      window.location.href = reqUrl
    }
  }
}

const goToPromotionDetail = (item) => {
  const { reqUrl } = item
  // 解析 reqUrl
  const urlObj = new URL(reqUrl)
  const currentUrl = window.location.href
  // 更新或添加参数
  urlObj.searchParams.set('goodsId', goodsId)
  urlObj.searchParams.set('skuId', currentSku.value.skuId)
  urlObj.searchParams.set('callback', encodeURIComponent(currentUrl))

  // 获取完整URL
  const url = urlObj.toString()
  // 判断当前商品是否有货
  if (!stockState.value) {
    showToast('抱歉，所选商品暂时无货，请选择其他商品办理。')
  } else {
    try {
      window.location.href = url
    } catch (error) {
      console.error('跳转保证金页面失败:', error)
      showToast('跳转失败，请稍后再试')
    }
  }
}

// 方法
const selectSpec = (spec) => {
  console.log('选择规格:', spec)

  // 如果传入的是完整的spec对象
  if (spec && spec.skuData) {
    curSkuId.value = spec.skuData.skuId

    // 更新当前规格
    const { param, param1, param2, param3 } = spec.skuData
    curSpecs.value = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
      .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
        p !== '_p2_undefined' && p !== '_p3_undefined')

    // 切换规格时重置数量为1，但要考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1

  } else if (typeof spec === 'string') {
    // 处理默认规格的情况
    if (spec === '默认规格') {
      // 对于默认规格，不需要特殊处理，保持当前状态
      // 如果没有规格数据，curSpecs应该为空数组
      if (!querySpecsList() || querySpecsList().length === 0 || querySpecsList()[0].length === 0) {
        curSpecs.value = []
      }
    } else {
      // 如果传入的是规格字符串，使用原有的setSpecs方法
      setSpecs(spec)
    }

    // 切换规格时重置数量为1，但要考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1
  }

  // 触发相关数据更新和滚动
  nextTick(() => {
    debouncedUpdateGoodsInfo()
  })

  // 延迟执行滚动，确保DOM完全更新
  setTimeout(() => {
    scrollToSelectedSpec()
  }, 100)
}

// 自动滚动到选中的规格
const scrollToSelectedSpec = async () => {
  console.log('开始执行 scrollToSelectedSpec')

  // 等待多个 tick 确保 DOM 完全更新
  await nextTick()
  await nextTick()

  if (!specOptionsRef.value) {
    console.log('specOptionsRef 不存在')
    return
  }

  // 修复选择器，使用正确的类名
  const selectedSpecElement = specOptionsRef.value.querySelector('.spec-option.is-active')
  console.log('找到的选中元素:', selectedSpecElement)

  if (!selectedSpecElement) {
    console.log('未找到选中的规格元素，尝试查找所有规格元素')
    const allSpecs = specOptionsRef.value.querySelectorAll('.spec-option')
    console.log('所有规格元素:', allSpecs)
    return
  }

  const container = specOptionsRef.value
  const containerWidth = container.clientWidth
  const selectedElementLeft = selectedSpecElement.offsetLeft
  const selectedElementWidth = selectedSpecElement.offsetWidth

  console.log('滚动计算参数:', {
    containerWidth,
    selectedElementLeft,
    selectedElementWidth,
    scrollWidth: container.scrollWidth
  })

  // 计算需要滚动的距离，让选中的规格居中显示
  const targetScrollLeft = selectedElementLeft - (containerWidth / 2) + (selectedElementWidth / 2)

  // 确保滚动位置不会超出边界
  const maxScrollLeft = container.scrollWidth - containerWidth
  const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

  console.log('滚动目标位置:', {
    targetScrollLeft,
    maxScrollLeft,
    finalScrollLeft
  })

  // 使用 requestAnimationFrame 确保在正确的时机执行滚动
  requestAnimationFrame(() => {
    console.log('执行滚动到位置:', finalScrollLeft)
    container.scrollTo({
      left: finalScrollLeft,
      behavior: 'smooth'
    })
  })
}


// 添加数量变化处理方法
const handleQuantityChange = (quantity) => {
  const xg = xgObj.value
  const lowestBuy = lowestBuyObj.value

  // 验证起购要求
  if (lowestBuy.isLowestBuy && quantity < lowestBuy.lowestBuyNum) {
    console.warn(`最少购买${lowestBuy.lowestBuyNum}件哦！`)
    goodsNum.value = lowestBuy.lowestBuyNum
    return
  }

  // 验证限购要求
  if (xg.isXg && quantity > xg.limitNum) {
    console.warn(`超出限购数量：${xg.limitText}`)
    goodsNum.value = xg.limitNum
    return
  }

  goodsNum.value = quantity
}

const handleAddToCart = async () => {
  // 检查登录状态
  await userStore.queryLoginStatus()
  if (!userStore.isLogin) {
    // 未登录，引导用户登录
    const loginSuccess = await userStore.login()
    if (!loginSuccess) {
      // 用户取消登录或登录失败
      return
    }
  }
  showSpecPopup.value = false
  const isPassed = await addressCheck()
  if (!isPassed) {
    return
  }

  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  // 需要判断商品状态 state： 0-不能购买，1-上架，2-下架，null-状态异常
  if (currentSku.value.state !== '1') {
    showToast('商品已下架，看看其他商品吧')
    return
  }

  // 检查库存状态
  if (!currentSku.value.stock || currentSku.value.stock <= 0) {
    showToast('商品暂时无货，看看其他商品吧')
    return
  }


  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  showLoadingToast()

  try {
    const err = await newCartStore.add({
      goodsId: currentSku.value.goodsId,
      skuId: currentSku.value.skuId,
      goodsNum: goodsNum.value,
      addressInfo
    })
    closeToast()

    if (err) {
      // 添加购物车失败
      showToast(err.msg)
    } else {
      // 添加购物车成功
      setTimeout(() => {
        showToast('加入购物车成功')
        if (lowestBuyObj.value.isLowestBuy) {
          goodsNum.value = +lowestBuyObj.value.lowestBuyNum
        } else {
          goodsNum.value = 1
        }
      }, 0)
    }
  } catch (error) {
    showToast('添加购物车失败，请重试')
  }
}

const handleBuyNow = async () => {
  // 检查登录状态
  await userStore.queryLoginStatus()
  if (!userStore.isLogin) {
    // 未登录，引导用户登录
    const loginSuccess = await userStore.login()
    if (!loginSuccess) {
      // 用户取消登录或登录失败
      return
    }
  }
  showSpecPopup.value = false
  const isPassed = await addressCheck()
  if (!isPassed) {
    return
  }

  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  // 需要判断商品状态 state： 0-不能购买，1-上架，2-下架，null-状态异常
  if (currentSku.value.state !== '1') {
    showToast('商品已下架，看看其他商品吧')
    return
  }

  // 检查库存状态
  if (!currentSku.value.stock || currentSku.value.stock <= 0) {
    showToast('商品暂时无货，看看其他商品吧')
    return
  }

  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  showLoadingToast()

  try {
    const [res] = await getBuyNowGoods({
      goodsId: currentSku.value.goodsId,
      skuId: currentSku.value.skuId,
      goodsNum: goodsNum.value,
      addressInfo,
      bizCode: getBizCode('ORDER')
    })
    closeToast()
    if (res?.code !== '0000') {
      if (res?.code === '1003') {
        await $alert({
          title: '',
          message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
          confirmButtonText: '修改地址',
          cancelButtonText: '取消',
          showCancelButton: true,
          confirmButtonColor: '#007EE6',
          cancelButtonColor: '#007EE6',
          onConfirmCallback: () => {
            router.push({
              name: 'address-edit',
              query: {
                addrId: info.addressId,
                isInvalid: '1'
              }
            })
          }
        })
      } else {
        showToast(res?.msg || '购买失败，请重试')
      }
    } else {
      // 购买成功
      // 缓存立即购买的数据
      buyProductNow.set(res)
      buyProductNowSession.set(res)

      // 调用setFrontCache接口缓存数据
      showLoadingToast()
      try {
        await setFrontCache({
          content: JSON.stringify(res)
        })
      } catch (cacheError) {
        console.error('缓存数据失败:', cacheError)
      } finally {
        closeToast()
      }

      // 直接下单，给订单页传参数用于订单页回显数据
      const query = {
        goodsId: currentSku.value.goodsId,
        skuId: currentSku.value.skuId,
        goodsNum: goodsNum.value,
        supplierCode: currentSku.value.supplierCode
      }

      // 如果当前页面URL中包含这些参数，则添加到query对象中
      if (route.query.curSelectedMoney) {
        query.curSelectedMoney = route.query.curSelectedMoney
      }
      if (route.query.curSelectedTime) {
        query.curSelectedTime = route.query.curSelectedTime
      }
      if (route.query.orderNo) {
        query.orderNo = route.query.orderNo
      }

      router.push({
        path: '/orderconfirm',
        query
      })
      // 重置商品数量
      if (lowestBuyObj.value.isLowestBuy) {
        goodsNum.value = +lowestBuyObj.value.lowestBuyNum
      } else {
        goodsNum.value = 1
      }
    }
  } catch (error) {
    showToast('购买失败，请重试')
  }
}

const addToCart = () => {
  if (cartButtonDisabledStatus.value) {
    return
  }
  specActionType.value = 1
  showSpecPopup.value = true
}

const goToCart = () => {
  router.push({ name: 'cart' })
}

const buyNow = () => {
  if (cartButtonDisabledStatus.value) {
    return
  }
  specActionType.value = 2
  showSpecPopup.value = true
}

// 轮播相关方法
const handleImagePreview = ({ item, index }) => {
  // 图片预览逻辑
  if (item.type === 'image') {
    previewImageIndex.value = index
    // 可以在这里实现图片预览功能
  }
}

const handleVideoPlay = ({ item }) => {
  // 视频播放逻辑
  console.log('播放视频:', item)
  // 可以打开视频播放器或全屏播放
}

const handleSlideChange = (index) => {
  // 轮播切换回调
  console.log('当前轮播索引:', index)
}

// 地址相关方法
const handleAddressClick = () => {
  // 点击配送区域，打开地址选择弹窗
  showAddressPopup.value = true
}

const handleAddressSelect = async (address) => {
  // 地址选择完成回调
  console.log('选择了新地址:', address)

  // 地址变更后重新查询商品相关信息
  await reloadGoodsInfoAfterAddressChange()
}

const handleAddressPopupClose = () => {
  // 地址弹窗关闭回调
  showAddressPopup.value = false
}

// 地址变更后重新查询商品信息
const reloadGoodsInfoAfterAddressChange = async () => {
  try {
    // 如果是京东商品，需要重新查询物流信息
    if (isJD.value) {
      // 重新查询物流配送时间
      await queryPredictSku()

      // 重新检查SKU是否可售
      await checkIsSkuSale()
    }

    // 重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 重新查询商品限制销售区域
    await querySale()

    console.log('地址变更后商品信息更新完成')
  } catch (error) {
    console.error('地址变更后更新商品信息失败:', error)
  }
  if(userStore.isLogin) {
    await addressCheck()
  }
}

// 商品状态计算属性
const onSaleState = computed(() => {
  // state： 0-不能购买，1-上架，2-下架，null-状态异常
  return !(currentSku.value && currentSku.value.state === '2')
})

const stockState = computed(() => {
  return currentSku.value && currentSku.value.stock > 0
})

const userStatus = computed(() => {
  // 用户购买资格检查
  // 如果商品需要检查白名单用户且用户已登录，则返回limitState的值
  const goodsDetail = spu.value
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1' && userStore.isLogin) {
    return limitState.value
  }
  // 其他情况默认有购买资格
  return true
})

// 购物车按钮禁用状态
const cartButtonDisabledStatus = computed(() => {
  return isDataGet.value ?
    !onSaleState.value || !stockState.value || !userStatus.value || !regionalSalesState.value || !limitState.value :
    true
})

// 监听地址变化，重新查询物流信息
const watchAddress = computed(() => userStore.curAddressInfo)
watch(watchAddress, async (newAddr, oldAddr) => {
  if (newAddr && oldAddr && (
    newAddr.provinceId !== oldAddr.provinceId ||
    newAddr.cityId !== oldAddr.cityId ||
    newAddr.countyId !== oldAddr.countyId ||
    newAddr.townId !== oldAddr.townId
  )) {
    if (isJD.value) {
      await queryPredictSku()
    }
    // 新增：地址切换时调用购物车query
    if (userStore.isLogin) {
      try {
        await newCartStore.query()
        console.log('地址切换后购物车数据已更新')
      } catch (error) {
        console.error('地址切换后更新购物车数据失败:', error)
      }
    }
  }
}, { deep: true })
// 监听规格数据变化
watch([curSpecs, () => queryDisabledSpecs()], ([newCurSpecs, newDisabledSpecs]) => {
  console.log('规格数据更新:', {
    curSpecs: newCurSpecs,
    disabledSpecs: newDisabledSpecs
  })
}, { deep: true })

// 监听当前SKU ID变化，触发滚动
watch(curSkuId, (newSkuId, oldSkuId) => {
  if (newSkuId && newSkuId !== oldSkuId && initialLoadComplete.value) {
    console.log('SKU ID 变化，触发滚动:', { newSkuId, oldSkuId })
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 150)
  }
})

// 监听登录状态变化，重新检查白名单用户限制
watch(() => userStore.isLogin, async (newLoginStatus, oldLoginStatus) => {
  if (newLoginStatus !== oldLoginStatus && spu.value) {
    // 登录状态发生变化，重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 如果用户登录了，重新获取营销活动信息
    if (newLoginStatus && marketTemplatesType1.value.length > 0) {
      await getElectronic()
    }

    // 新增：登录状态变化时调用购物车query
    if (newLoginStatus) {
      try {
        await newCartStore.query()
        console.log('登录后购物车数据已更新')
      } catch (error) {
        console.error('登录后更新购物车数据失败:', error)
      }
    }
  }
})

onMounted(async () => {
  loadGoodsDetail()
  // 监听滚动事件，实时保存滚动位置
  window.addEventListener('scroll', saveScrollPosition, { passive: true })

  // 新增：页面进入时判断登录状态，如果登录了就查询购物车
  if (userStore.isLogin) {
    try {
      await newCartStore.query()
      console.log('页面加载时购物车数据已更新')
    } catch (error) {
      console.error('页面加载时更新购物车数据失败:', error)
    }
  }
})

onUnmounted(() => {
  // 清理滚动事件监听器
  window.removeEventListener('scroll', saveScrollPosition)
  window.removeEventListener('scroll', handleUserScroll)

  // 清理 Intersection Observer
  if (introduceObserver) {
    introduceObserver.disconnect()
    introduceObserver = null
  }
})

// 分享初始化
const shareInit = async () => {
  const bizCode = getBizCode()
  const intro = () => {
    const sku = spu.value?.skuList?.[0] || currentSku.value
    if (bizCode === 'ziying') {
      return sku.comment || '足不出户囤遍好物！购商品，来精选。'
    } else if (bizCode === 'fupin') {
      return sku.comment || '消费帮扶，共献爱心，乡村振兴，有我联通。'
    } else if (bizCode === 'fulihui') {
      return sku.comment || '足不出户囤遍好物！购商品，来福利汇。'
    } else if (bizCode === 'lnzx') {
      return sku.comment || '联农智选，好货甄选，品质可信。'
    } else {
      return sku.comment || sku.name || sku.merchantName || ''
    }
  }

  const firstSku = spu.value?.skuList?.[0] || currentSku.value
  shareData.title = firstSku.name || goodsInfo.value.name
  shareData.describe = intro()
  shareData.picUrl = goodsInfo.value.imageUrl || ''
  shareData.link = await getDefaultShareUrl()
  log('[GOODS-DETAIL] shareInfo', shareData)
  setWeiXinShareData(shareData)
}

// 点击右上角分享
const onDropdownShare = (e) => {
  // 分享功能，不能写在异步函数中
  share(shareData, e)
}

// 营销活动相关计算属性
const hasMarketingType1 = computed(() => {
  return marketTemplatesType1.value && marketTemplatesType1.value.length > 0
})

const hasMarketingType4 = computed(() => {
  return marketTemplatesType4.value && marketTemplatesType4.value.length > 0
})
</script>

<style scoped lang="less">
.goods-detail {
  min-height: 100vh;
  background-color: @bg-color-white;
  padding-bottom: 55px;
}

.goods-content {
  background-color: @bg-color-white;
}

.image-section {
  background-color: @bg-color-white;
  padding: 0;
}

.basic-info-section {
  background-color: @bg-color-white;
  padding: 5px 17px;
  box-sizing: border-box;
}

.marketing-section {
  background-color: @bg-color-white;
  padding: 5px 0;
  box-sizing: border-box;
}

.spec-section {
  background-color: @bg-color-white;
  padding: 5px 17px;
  box-sizing: border-box;
}

.promotion-section {
  background-color: @bg-color-white;
  padding: 5px 0;
  box-sizing: border-box;
}

.delivery-section {
  background-color: @bg-color-white;
  padding: 5px 17px;
  box-sizing: border-box;
}

.price-wrapper {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 10px;

  .price-original {
    font-size: @font-size-14;
    color: #999;
    text-decoration: line-through;
  }
}

.marketing-wrapper {
  margin-bottom: 12px;

  .marketing-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #ff7a0a, #ff9500);
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(255, 122, 10, 0.3);
    }

    .marketing-text {
      font-size: @font-size-14;
      color: white;
      font-weight: 500;
    }

    .marketing-price {
      font-size: @font-size-12;
      color: white;
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 8px;
      border-radius: 12px;
    }
  }
}

.title {
  font-size: @font-size-16;
  color: @text-color-primary;
  font-weight: @font-weight-500;
  line-height: 1.5;
  .multi-ellipsis(2);
}

.spec-wrapper {
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  .spec-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    margin-top: 2px;
  }

  .spec-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .spec-selected {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    margin-bottom: 12px;

    .spec-label {
      font-size: @font-size-13;
      color: @text-color-secondary;
      margin-right: 4px;
    }

    .spec-value {
      font-size: @font-size-13;
      color: @text-color-primary;
      flex: 1;
    }

    .arrow-icon {
      width: 5px;
      height: 9px;
    }
  }
}

.spec-options-wrapper {
  overflow: hidden;
  display: flex;
  align-items: center;
  .spec-options {
    flex: 1;
    display: flex;
    gap: 12px;
    overflow-x: auto;
    overflow-y: hidden;

    &::-webkit-scrollbar {
      height: 0px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    .spec-option {
      position: relative;
      width: 60px;
      height: 60px;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      border: 2px solid transparent;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;

      &.is-active {
        border-color: #ff7a0a;
      }

      &.active {
        border-color: #ff7a0a;
      }

      &.is-disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .spec-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .spec-name {
        font-size: @font-size-11;
        color: @text-color-primary;
        text-align: center;
        padding: 2px;
        .ellipsis();
      }

      .spec-check {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 16px;
        height: 16px;
      }
    }
  }

  .spec-count {
    flex-shrink: 0;
    font-size: @font-size-12;
    color: #999;
    margin-left: 5px;
  }
}

.delivery-wrapper {
  .delivery-main {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;

    .delivery-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      margin-top: 2px;
    }

    .delivery-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow: hidden;
    }
  }

  .delivery-location,
  .delivery-time,
  .delivery-return,
  .delivery-service {
    display: flex;
    align-items: center;
    font-size: @font-size-13;
  }

  .delivery-location {
    .delivery-text {
      font-size: @font-size-13;
      color: @text-color-primary;
      font-weight: 500;
    }
  }

  .delivery-time {
    .delivery-badge {
      width: 64px;
      height: 20px;
      margin-right: 8px;
    }

    .delivery-text {
      color: @text-color-secondary;
      flex: 1;
      .ellipsis();
    }

    .arrow-icon {
      width: 5px;
      height: 9px;
      margin-left: 4px;
      flex-shrink: 0;
    }
  }

  .delivery-return {
    margin-bottom: 12px;

    .return-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .delivery-text {
      color: @text-color-secondary;
    }
  }

  .delivery-service {
    .service-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .delivery-text {
      color: @text-color-secondary;

      .text-highlight {
        color: #ff7a0a;
      }
    }
  }
}

// 商品介绍区域 - 标准padding
.introduce-section {
  background-color: @bg-color-white;
  padding: 10px 0;
  box-sizing: border-box;
}

.introduce-placeholder {
  padding: 20px;
  background-color: @bg-color-white;

  &__title {
    font-size: @font-size-16;
    font-weight: @font-weight-500;
    color: @text-color-primary;
    margin-bottom: 16px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__skeleton {
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 8px;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: @bg-color-white;
  padding: 0 17px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 0, 0, 0.05);

  // 状态提示样式
  .tips-state,
  .tips-stock {
    width: 100%;
    padding: 8px 0;
    text-align: center;
    font-size: @font-size-13;
    color: #ff4444;
    background-color: #fff2f0;
    border-bottom: 1px solid #ffccc7;
  }

  .tips-state {
    background-color: #fff2f0;
    color: #ff4444;
  }

  .tips-stock {
    background-color: #fff7e6;
    color: #fa8c16;
  }

  // 操作区域
  .action-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .cart-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0 0 0;
    box-sizing: border-box;

    .cart-icon-wrapper {
      position: relative;

      .cart-icon {
        width: 24px;
        height: 24px;
      }

      .cart-badge {
        :deep(.van-badge) {
          background-color: @theme-color;
          border-color: @theme-color;
        }
      }

      // 购物车数量显示
      .num {
        position: absolute;
        top: -8px;
        right: -2px;
        min-width: 16px;
        height: 16px;
        background-color: #ff4444;
        color: white;
        border-radius: 8px;
        font-size: @font-size-11;
        line-height: 16px;
        text-align: center;
        padding: 0 4px;
        box-sizing: border-box;
        z-index: 10;

        &.animation {
          animation: shake 0.6s ease-in-out;
        }
      }
    }

    .cart-text {
      font-size: @font-size-11;
      color: @text-color-secondary;
    }
  }

  .buttons-wrapper {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}

// 购物车震动动画
@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

// 状态提示样式
.status-tips-overlay {
  position: fixed;
  bottom: 49px;
  left: 0;
  right: 0;
  z-index: 999;
  pointer-events: none;

  .status-tips-container {
    // padding: 0 16px;
    pointer-events: auto;

    .status-tip {
      display: flex;
      align-items: flex-start;
      background: rgba(0, 0, 0, 0.85);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 10px;
      margin-bottom: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      animation: slideInUp 0.3s ease-out;

      .tip-icon {
        font-size: 24px;
        margin-right: 12px;
        flex-shrink: 0;
        line-height: 1;
      }

      .tip-content {
        flex: 1;

        .tip-title {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
          line-height: 1.3;
        }

        .tip-message {
          color: rgba(255, 255, 255, 0.8);
          font-size: 14px;
          line-height: 1.4;
        }
      }

      // 不同状态的主题色
      &.tips-state {
        border-left: 4px solid #ff6b6b;

        .tip-icon {
          filter: hue-rotate(0deg);
        }
      }

      &.tips-stock {
        border-left: 4px solid #ffa726;

        .tip-icon {
          filter: hue-rotate(30deg);
        }
      }

      &.tips-permission {
        border-left: 4px solid #ef5350;

        .tip-icon {
          filter: hue-rotate(0deg);
        }
      }

      &.tips-region {
        border-left: 4px solid #42a5f5;

        .tip-icon {
          filter: hue-rotate(200deg);
        }
      }

      &.tips-limit {
        border-left: 4px solid #ab47bc;

        .tip-icon {
          filter: hue-rotate(280deg);
        }
      }
    }
  }
}

// 滑入动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式适配
@media (max-width: 375px) {
  .status-tips-overlay {
    .status-tips-container {
      padding: 0 5px;

      .status-tip {
        padding: 10px;

        .tip-icon {
          font-size: 20px;
          margin-right: 10px;
        }

        .tip-content {
          .tip-title {
            font-size: 15px;
          }

          .tip-message {
            font-size: 13px;
          }
        }
      }
    }
  }
}

// 优惠活动样式
.promotion-activity {
  background: linear-gradient(90deg, #FF6362 0%, #FFBE7A 100%);
  padding: 10px;
  .promotion-activity-title {
    color: #FFFFFF;
    font-size: 16px;
    font-weight: @font-weight-500;
    margin-bottom: 8px;
  }

  .promotion-activity-content {
    display: flex;
    align-items: center;
    border-radius: 8px;
    padding: 13px;
    background: linear-gradient(90deg, #FFEEEB 0%, #FFFAF9 100%);

    .promotion-item {
      display: flex;
      align-items: center;
      width: 100%;
      // min-height: 60px;
      margin-bottom: 15px;
      border-bottom: 1px solid #F5F5F5;
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .promotion-heart-img {
        width: 25px;
        height: 20px;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .promotion-content {
        flex: 1;
        font-size: 14px;
        color: @text-color-primary;
        font-weight: 500;
        line-height: 1.4;
      }

      .promotion-arrow {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .promotion-desc {
          font-size: 14px;
          color: #FF780A;
          margin-right: 8px;
        }

        .arrow-icon {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-top: 2px solid #FF780A;
          border-right: 2px solid #FF780A;
          transform: rotate(45deg);
        }
      }
    }
  }
}

.goods-marketing {
  img {
    width: 100vw; // 使图片宽度等于屏幕宽度
    height: auto;
    display: block;
    cursor: pointer;
  }
}
</style>
